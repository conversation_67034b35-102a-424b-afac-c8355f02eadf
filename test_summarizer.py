#!/usr/bin/env python3
"""
Test script for the AI summarizer without requiring actual API keys.
This creates a mock version that demonstrates the functionality.
"""

import os
import json
from pathlib import Path
from datetime import datetime, timezone
from ai_summarizer import TranscriptData, MeetingData, MeetingSummary, AISummarizer

# Mock AI response for testing
MOCK_AI_RESPONSE = """{
    "meeting_metadata": {
        "title": "Business Strategy Discussion with <PERSON><PERSON>",
        "attendees": ["<PERSON>", "<PERSON><PERSON>"],
        "absent": [],
        "date_processed_utc": "2024-08-02T10:30:00Z"
    },
    "executive_summary": "Discussion focused on completing the intake form for data collection, pricing strategy adjustments based on client feedback, and organizational changes to improve engagement manager efficiency. Key decisions included implementing a $1 automated package option and adding a coordinator role to handle platform setup tasks.",
    "outcomes": [
        {
            "decision": "Implement automated $1 package option for RFPs",
            "owner": "Development Team",
            "rationale": "To remain competitive in RFP processes while maintaining higher-tier pricing for full-service offerings",
            "context": "USI feedback indicated pricing concerns, and competitors like Willow offer basic platform-only options",
            "actions": [
                {
                    "owner": "Development Team",
                    "task": "Complete intake form for data collection with basic client information fields",
                    "deadline": "Not specified",
                    "priority": "HIGH"
                },
                {
                    "owner": "Development Team", 
                    "task": "Implement automated onboarding process for $1 package",
                    "deadline": "Not specified",
                    "priority": "HIGH"
                }
            ]
        },
        {
            "decision": "Add coordinator role (Kia) to handle platform setup tasks",
            "owner": "Nima Bousheri",
            "rationale": "To reduce engagement manager workload and improve challenge setup accuracy",
            "context": "Engagement managers struggle with learning curve for platform functionality and proper challenge setup",
            "actions": [
                {
                    "owner": "Nima Bousheri",
                    "task": "Onboard Kia as platform coordinator for challenge setup",
                    "deadline": "Not specified", 
                    "priority": "MEDIUM"
                }
            ]
        }
    ],
    "open_questions": [
        {
            "question": "Should we attend the September event in Vegas?",
            "owner": "Team",
            "status": "Needs discussion"
        },
        {
            "question": "What are the mysterious Amazon charges on the Southwest card?",
            "owner": "Nima Bousheri",
            "status": "Needs investigation"
        }
    ],
    "working_sessions_needed": [
        {
            "topic": "Premium package development with disease management features",
            "participants": "Development team",
            "goal": "Review code status and implement emotional well-being modules"
        },
        {
            "topic": "Marketing budget adjustments and broker targeting strategy", 
            "participants": "Marketing team",
            "goal": "Reallocate resources to target specific high-value brokers"
        }
    ]
}"""

class MockAISummarizer(AISummarizer):
    """Mock version of AISummarizer for testing without API keys."""
    
    def __init__(self):
        # Skip the parent __init__ to avoid API initialization
        self.vertex_model = None
        self.gemini_model = None
        self.ai_available = True  # Pretend AI is available
        from ai_summarizer import settings
        self.settings = settings
    
    def _call_ai_model(self, prompt: str) -> str:
        """Return mock AI response instead of calling real API."""
        print("🤖 Using mock AI response (no API key required)")
        return MOCK_AI_RESPONSE

def test_summarizer():
    """Test the summarizer with mock data."""
    print("🚀 Testing AI Summarizer...")
    
    # Sample transcript text
    transcript_text = """Call with Nima Bousheri

You
OK, I think

Nima Bousheri
OK so here I'll give you a task here so check to see what because I know they they started building something. It's not completely from scratch. I think they built something but I'd like to get the intake form again just for data collection and intake form that I think you already have all the specs which says like client name address your number of life start of the campaign end of the campaign. What son is just basics very basic and see what we could do about getting that completed because we had a lot of feedback from not only USI but like Seabi like 10 people from Seabi showed up to that

You
Yeah,

Nima Bousheri
That happy hour that we had

You
that real quick yeah tell me about the happy hour man how'd it go?

Nima Bousheri
So it was good they had like 10 brokers from sea biz they had some gallery like 34 people from Gallagher show up so it was it was good to set up was awesome lately I had a CK like set up the table. Send pictures of it like set up the table with all of the finger foods and burgers and all that we did at STK in San Diego."""
    
    # Create transcript data
    transcript_data = TranscriptData(
        file_path=Path("test_meeting.txt"),
        content=transcript_text,
        metadata={
            "word_count": len(transcript_text.split()),
            "meeting_time": datetime.now().isoformat(),
            "participants": ["You", "Nima Bousheri"]
        }
    )
    
    # Use mock summarizer
    summarizer = MockAISummarizer()
    
    try:
        # Generate summary
        print("📝 Generating meeting summary...")
        summary = summarizer.summarize_transcript(transcript_data)
        
        print("✅ Summary generated successfully!")
        print(f"📧 Email subject: {summary.get_email_subject()}")
        print(f"👥 Participants: {summary.get_participants_summary()}")
        print(f"📄 HTML file: {summary.meeting_data.html_file_path}")
        print(f"📊 JSON file: {summary.meeting_data.json_file_path}")
        
        # Display key outcomes
        print("\n🎯 Key Outcomes:")
        for i, outcome in enumerate(summary.meeting_data.outcomes, 1):
            print(f"  {i}. {outcome['decision']} (Owner: {outcome['owner']})")
        
        # Display action items
        print("\n📋 Action Items:")
        action_count = 0
        for outcome in summary.meeting_data.outcomes:
            for action in outcome.get('actions', []):
                action_count += 1
                print(f"  {action_count}. {action['task']} (Owner: {action['owner']}, Priority: {action['priority']})")
        
        print(f"\n🎉 Test completed! Files saved in 'meeting_summaries' directory.")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_summarizer()
    if success:
        print("\n✨ All tests passed! The summarizer is working correctly.")
    else:
        print("\n💥 Tests failed. Please check the error messages above.")
