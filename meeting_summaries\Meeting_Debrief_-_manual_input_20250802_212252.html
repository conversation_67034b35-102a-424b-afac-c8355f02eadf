<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Debrief - manual_input - Meeting Debrief</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1a1a1a;
            margin-bottom: 8px;
            font-size: 28px;
            font-weight: 600;
        }
        .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
        }
        .metadata {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 32px;
            border: 1px solid #e9ecef;
        }
        .metadata p {
            margin: 4px 0;
            font-size: 14px;
        }
        .metadata strong {
            font-weight: 600;
        }
        h2 {
            color: #1a1a1a;
            font-size: 20px;
            font-weight: 600;
            margin: 32px 0 16px 0;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 24px;
        }
        .outcome {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }
        .outcome-title {
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        .outcome-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        .context {
            font-style: italic;
            color: #666;
            font-size: 14px;
            border-left: 3px solid #e9ecef;
            padding-left: 12px;
            margin-top: 12px;
        }
        .owner-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .owner-header {
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .owner-name {
            font-weight: 600;
            color: #1a1a1a;
        }
        .task-count {
            font-size: 12px;
            color: #666;
        }
        .tasks-table {
            width: 100%;
            border-collapse: collapse;
        }
        .tasks-table th {
            background: #f8f9fa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }
        .tasks-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }
        .priority {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .priority-high {
            background: #fee;
            color: #c53030;
        }
        .priority-medium {
            background: #fffbeb;
            color: #d69e2e;
        }
        .priority-low {
            background: #f0f9ff;
            color: #2b6cb0;
        }
        .question {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        .session {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        .no-items {
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Meeting Debrief - manual_input</h1>
        <div class="subtitle">Debrief generated on 08/02/2025, 03:52:52 PM</div>

        <div class="metadata">
            <p><strong>Present:</strong> None specified</p>
            <p><strong>Absent:</strong> None</p>
        </div>

        <h2>Executive Summary</h2>
        <div class="summary">
            This meeting transcript contains 2607 words of discussion. The AI summarization service encountered an issue processing this transcript, but the raw content is available for manual review.
        </div>

        <h2>Key Outcomes</h2>
        
            <div class="outcome">
                <div class="outcome-title">Manual review required</div>
                <div class="outcome-meta">
                    <strong>Owner:</strong> Meeting organizer |
                    <strong>Rationale:</strong> AI processing encountered an issue with this transcript
                </div>
                <div class="context"><strong>Context:</strong> Please review the original transcript for detailed meeting content</div>
            </div>
            

        <h2>Tasks</h2>
        
            <div class="owner-section">
                <div class="owner-header">
                    <div class="owner-name">Meeting organizer</div>
                    <div class="task-count">1 tasks, 0 high priority</div>
                </div>
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>Task</th>
                            <th>Deadline</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                <tr>
                    <td>Review transcript and extract key decisions manually</td>
                    <td>Not specified</td>
                    <td><span class="priority priority-medium">MEDIUM</span></td>
                </tr>
                
                    </tbody>
                </table>
            </div>
            

        <h2>Open Questions</h2>
        
            <div class="question">
                <div style="font-weight: 600; margin-bottom: 8px;">What were the key decisions and action items from this meeting?</div>
                <div style="font-size: 14px; color: #666;">
                    <strong>Owner:</strong> Meeting organizer |
                    <strong>Status:</strong> Requires manual transcript review
                </div>
            </div>
            

        <h2>Working Sessions Needed</h2>
        <div class="no-items">No working sessions needed.</div>
    </div>
</body>
</html>