#!/usr/bin/env python3
"""
Simple script to test email authentication without running the full AI summarizer.
This helps isolate email authentication issues.
"""

import smtplib
from email.message import EmailMessage
import sys

def test_email_auth(sender_email, smtp_password, recipient_email, smtp_host="smtp.gmail.com", smtp_port=587):
    """Test email authentication and send a simple test message."""
    
    print(f"Testing email authentication for: {sender_email}")
    print(f"SMTP Server: {smtp_host}:{smtp_port}")
    print(f"Recipient: {recipient_email}")
    print("-" * 50)
    
    try:
        # Create a simple test message
        msg = EmailMessage()
        msg['Subject'] = "Test Email - Authentication Check"
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg.set_content("This is a test email to verify SMTP authentication is working correctly.")
        
        print("1. Creating SMTP connection...")
        with smtplib.SMTP(smtp_host, smtp_port) as smtp:
            print("2. Starting TLS encryption...")
            smtp.starttls()
            
            print("3. Attempting login...")
            smtp.login(sender_email, smtp_password)
            print("✓ Login successful!")
            
            print("4. Sending test message...")
            smtp.send_message(msg)
            print("✓ Message sent successfully!")
            
        print(f"\n✅ SUCCESS: Test email sent to {recipient_email}")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"\n❌ AUTHENTICATION ERROR: {e}")
        print("\n🔧 SOLUTIONS:")
        print("1. For Gmail accounts:")
        print("   - Enable 2-Factor Authentication")
        print("   - Generate an App Password: https://support.google.com/accounts/answer/185833")
        print("   - Use the App Password instead of your regular password")
        print("\n2. Alternative solutions:")
        print("   - Check if email and password are correct")
        print("   - Ensure account is not locked or suspended")
        return False
        
    except smtplib.SMTPException as e:
        print(f"\n❌ SMTP ERROR: {e}")
        return False
        
    except Exception as e:
        print(f"\n❌ GENERAL ERROR: {e}")
        return False

if __name__ == "__main__":
    # Test with the credentials from your script
    sender_email = "<EMAIL>"
    smtp_password = "<EMAIL>!@#"  # This might need to be an App Password
    recipient_email = "<EMAIL>"
    
    print("🔍 Email Authentication Test")
    print("=" * 50)
    
    success = test_email_auth(sender_email, smtp_password, recipient_email)
    
    if not success:
        print("\n📋 NEXT STEPS:")
        print("1. Go to your Google Account settings")
        print("2. Enable 2-Factor Authentication if not already enabled")
        print("3. Generate an App Password for 'Mail'")
        print("4. Replace the password in your script with the App Password")
        print("5. Run this test again")
        
    sys.exit(0 if success else 1)
