# AI Meeting Summarizer

This tool uses AI to automatically summarize meeting transcripts and generate professional HTML reports with action items, decisions, and follow-up tasks.

## 🚀 Quick Start (Testing Mode)

The easiest way to test the functionality is with the mock mode (no API keys required):

```bash
# Install dependencies
pip install -r requirements.txt

# Run the test
python run_with_mock.py
```

This will:
- ✅ Process your meeting transcript
- ✅ Generate a professional HTML summary
- ✅ Create a JSON file with structured data
- ✅ Show all action items and decisions
- ✅ Open the HTML report in your browser

## 📁 Output Files

The tool creates files in the `meeting_summaries/` directory:
- **HTML file**: Beautiful, professional meeting report
- **JSON file**: Structured data for integration with other tools

## 🔧 Using Real AI (Optional)

To use actual AI services instead of mock responses:

### Option 1: Google Gemini API (Recommended)
1. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Set environment variable:
   ```bash
   set GEMINI_API_KEY=your_api_key_here
   ```
3. Run: `python run_summary_email.py`

### Option 2: Google Cloud Vertex AI
1. Set up Google Cloud project
2. Enable Vertex AI API
3. Set environment variables:
   ```bash
   set GOOGLE_CLOUD_PROJECT_ID=your_project_id
   set VERTEX_AI_LOCATION=us-central1
   ```
4. Configure authentication (gcloud auth or service account)

## 📧 Email Integration

The `run_summary_email.py` script can also send the summary via email. Update the email settings in the script:

```python
summarize_and_send_email(
    transcript_text="Your meeting transcript here...",
    recipient_email="<EMAIL>",
    sender_email="<EMAIL>",
    smtp_password="your_app_password"
)
```

**Note**: For Gmail, use an [App Password](https://support.google.com/accounts/answer/185833) instead of your regular password.

## 🛠️ Files Overview

- `ai_summarizer.py` - Main AI summarization engine
- `run_summary_email.py` - Script to run with real AI and email
- `run_with_mock.py` - Test script with mock AI responses
- `test_summarizer.py` - Unit tests and mock AI implementation
- `requirements.txt` - Python dependencies

## 🎯 Features

- **Smart Analysis**: Extracts decisions, action items, and open questions
- **Professional Reports**: Beautiful HTML output with modern styling
- **Multiple AI Providers**: Supports both Gemini API and Vertex AI
- **Email Integration**: Automatically send summaries to stakeholders
- **Structured Data**: JSON output for integration with other tools
- **Fallback Handling**: Graceful degradation when AI services are unavailable

## 🔍 Example Output

The tool generates:
- Executive summary of the meeting
- Key decisions with owners and rationale
- Action items with priorities and deadlines
- Open questions that need follow-up
- Recommended working sessions
- Professional HTML report with modern styling

## 🚨 Troubleshooting

**"AI libraries not available"**: Run `pip install -r requirements.txt`

**"No AI models available"**: Set up API keys (see "Using Real AI" section above)

**Email issues**: Make sure to use App Passwords for Gmail, not regular passwords

**Permission errors**: Make sure the script has write access to create the `meeting_summaries` folder

## 💡 Tips

- Start with the mock mode (`run_with_mock.py`) to test everything works
- The HTML reports look great when printed or saved as PDF
- JSON files can be imported into project management tools
- Use environment variables to keep API keys secure
