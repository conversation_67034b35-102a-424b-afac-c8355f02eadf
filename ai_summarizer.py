"""AI service for summarizing meeting transcripts using Vertex AI and Gemini."""

import json
from typing import Dict, Optional, List, Any
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
import smtplib
from email.message import EmailMessage

import structlog

# Optional AI imports
try:
    import google.generativeai as genai
    from google.cloud import aiplatform
    from vertexai.generative_models import GenerativeModel
    AI_AVAILABLE = True
except ImportError as e:
    genai = None
    aiplatform = None
    GenerativeModel = None
    AI_AVAILABLE = False

# Configuration removed - using environment variables directly
from pathlib import Path

logger = structlog.get_logger(__name__)


@dataclass
class TranscriptData:
    """Data class for transcript information."""
    file_path: Path
    content: str
    metadata: Dict[str, Any]

    def get_summary_text(self) -> str:
        """Get the transcript content for summarization."""
        return self.content


# Create a settings-like object for backward compatibility
class Settings:
    def __init__(self):
        import os
        self._project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID", "meeting-intelligence-agent")
        self._location = os.getenv("VERTEX_AI_LOCATION", "us-central1")

    @property
    def email_subject_prefix(self):
        return "Meeting Summary:"

    @property
    def google_cloud_project_id(self):
        return self._project_id

    @property
    def vertex_ai_location(self):
        return self._location

    @property
    def gemini_api_key(self):
        import os
        return os.getenv("GEMINI_API_KEY")

    @property
    def upload_summaries_to_drive(self):
        import os
        return os.getenv("UPLOAD_SUMMARIES_TO_DRIVE", "false").lower() == "true"

settings = Settings()


@dataclass
class MeetingData:
    """Enhanced data class for comprehensive meeting debrief information."""
    meeting_metadata: Dict[str, Any]
    executive_summary: str
    outcomes: List[Dict[str, Any]]
    open_questions: List[Dict[str, Any]]
    working_sessions_needed: List[Dict[str, Any]]
    html_file_path: str = ""
    json_file_path: str = ""

    # Backward compatibility properties
    @property
    def action_items(self) -> List[Dict[str, Any]]:
        """Backward compatibility: map outcomes to action_items."""
        return self.outcomes

    @property
    def key_decisions(self) -> List[Dict[str, Any]]:
        """Backward compatibility: map outcomes to key_decisions."""
        return self.outcomes


class MeetingSummary:
    """Represents a meeting summary with enhanced debrief data and HTML formats."""

    def __init__(self, meeting_data: MeetingData, html_summary: str, transcript_data: TranscriptData):
        self.meeting_data = meeting_data
        self.html_summary = html_summary
        self.transcript_data = transcript_data

        # Backward compatibility - expose as json_summary
        self.json_summary = asdict(meeting_data)

        self.metadata = {
            'source_file': transcript_data.file_path.name,
            'word_count': transcript_data.metadata.get('word_count', 0),
            'participants': meeting_data.meeting_metadata.get('attendees', []),
            'meeting_time': transcript_data.metadata.get('meeting_time')
        }

    def get_email_subject(self) -> str:
        """Generate email subject for the summary."""
        meeting_title = self.meeting_data.meeting_metadata.get('title', 'Meeting Debrief')
        return f"{settings.email_subject_prefix} {meeting_title}"

    def get_participants_summary(self) -> str:
        """Get formatted participants list."""
        participants = self.meeting_data.meeting_metadata.get('attendees', [])
        if participants:
            return f"Participants: {', '.join(participants)}"
        return "Participants: Not specified"


class AISummarizer:
    """Service for AI-powered meeting transcript summarization."""
    
    def __init__(self):
        self.vertex_model = None
        self.gemini_model = None
        self.ai_available = AI_AVAILABLE
        self.settings = settings
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize AI models."""
        if not AI_AVAILABLE:
            raise ValueError("AI libraries not available. Please install with: pip install -r requirements.txt")

        try:
            # Initialize Vertex AI
            if settings.google_cloud_project_id and aiplatform:
                aiplatform.init(
                    project=settings.google_cloud_project_id,
                    location=settings.vertex_ai_location
                )
                self.vertex_model = GenerativeModel("gemini-1.5-pro")
                logger.info("Vertex AI Gemini model initialized")

            # Initialize Gemini API
            if settings.gemini_api_key and genai:
                genai.configure(api_key=settings.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                logger.info("Gemini API model initialized")

            if not self.vertex_model and not self.gemini_model:
                raise ValueError("No AI models available. Please configure Vertex AI or Gemini API.")

        except Exception as error:
            logger.error(f"Failed to initialize AI models: {error}")
            raise
    
    def summarize_transcript(self, transcript_data: TranscriptData) -> MeetingSummary:
        """
        Generate comprehensive meeting debrief from transcript using enhanced AI prompting.

        Args:
            transcript_data: Processed transcript data

        Returns:
            MeetingSummary object with structured debrief data and HTML
        """
        logger.info(f"Generating AI debrief for transcript: {transcript_data.file_path.name}")

        try:
            # Generate structured meeting debrief
            meeting_data = self._generate_meeting_debrief(transcript_data)

            # Generate professional HTML
            html_summary = self._generate_professional_html(meeting_data)

            # Save files locally
            self._save_summary_files(meeting_data, html_summary, transcript_data)

            summary = MeetingSummary(meeting_data, html_summary, transcript_data)
            logger.info("Successfully generated meeting debrief")

            return summary

        except Exception as error:
            logger.error(f"Failed to generate debrief: {error}")
            raise
    
    def _generate_meeting_debrief(self, transcript_data: TranscriptData) -> MeetingData:
        """Generate structured meeting debrief using enhanced AI prompting."""
        prompt = self._get_debrief_prompt(transcript_data)

        try:
            response = self._call_ai_model(prompt)
            response_text = response.strip()

            # Debug logging
            logger.info(f"Raw AI response length: {len(response_text)} characters")
            logger.debug(f"Raw AI response (first 500 chars): {response_text[:500]}")

            # Clean up the response text - remove markdown formatting if present
            original_response = response_text
            if response_text.startswith('```json'):
                response_text = response_text[7:]  # Remove ```json
            elif response_text.startswith('```'):
                response_text = response_text[3:]  # Remove ```
            if response_text.endswith('```'):
                response_text = response_text[:-3]  # Remove ```

            response_text = response_text.strip()

            # Try to find JSON content between curly braces
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                logger.error(f"No JSON object found in response. Full response: {original_response[:1000]}")
                raise ValueError("No JSON object found in response")

            json_str = response_text[start_idx:end_idx]
            logger.info(f"Extracted JSON string length: {len(json_str)} characters")
            logger.debug(f"Extracted JSON string (first 200 chars): {json_str[:200]}")

            try:
                meeting_data_dict = json.loads(json_str)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError as e:
                logger.error(f"JSON Parse Error: {e}")
                logger.error(f"Error location: {e.pos if hasattr(e, 'pos') else 'unknown'}")
                logger.error(f"Problematic part: {json_str[max(0, e.pos-20):e.pos+20] if hasattr(e, 'pos') else 'unknown'}")
                logger.error(f"Full JSON string: {json_str}")
                raise

            # Ensure date_processed_utc is set
            if "meeting_metadata" in meeting_data_dict:
                meeting_data_dict["meeting_metadata"]["date_processed_utc"] = datetime.now(timezone.utc).isoformat()

            return MeetingData(**meeting_data_dict)

        except Exception as error:
            logger.error(f"Failed to parse debrief response: {error}")
            # Return fallback structured data
            return self._create_fallback_meeting_data(transcript_data)
    
    def _generate_professional_html(self, meeting_data: MeetingData) -> str:
        """Generate professional HTML report from meeting data."""
        return self._create_professional_html_template(meeting_data)

    def _save_summary_files(self, meeting_data: MeetingData, html_summary: str, transcript_data: TranscriptData):
        """Save HTML and JSON summary files locally and optionally upload to Drive."""
        from pathlib import Path
        import json
        from datetime import datetime

        # Create output directory
        output_dir = Path("meeting_summaries")
        output_dir.mkdir(exist_ok=True)

        # Generate filename based on meeting title and timestamp
        meeting_title = meeting_data.meeting_metadata.get('title', 'Meeting')
        # Clean filename - remove invalid characters
        clean_title = "".join(c for c in meeting_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        clean_title = clean_title.replace(' ', '_')

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{clean_title}_{timestamp}"

        # Save HTML file
        html_file = output_dir / f"{base_filename}.html"
        try:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_summary)
            logger.info(f"Saved HTML summary to: {html_file}")
        except Exception as e:
            logger.error(f"Failed to save HTML file: {e}")

        # Save JSON file
        json_file = output_dir / f"{base_filename}.json"
        try:
            json_data = {
                "meeting_data": asdict(meeting_data),
                "transcript_metadata": {
                    "source_file": transcript_data.file_path.name,
                    "word_count": transcript_data.metadata.get('word_count', 0),
                    "processing_timestamp": datetime.now().isoformat()
                }
            }
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved JSON summary to: {json_file}")
        except Exception as e:
            logger.error(f"Failed to save JSON file: {e}")

        # Store file paths in meeting data for email attachment
        meeting_data.html_file_path = str(html_file)
        meeting_data.json_file_path = str(json_file)

        # Upload to Google Drive if enabled
        if settings.upload_summaries_to_drive:
            try:
                # TODO: Implement DriveUploadService or use existing Google Drive utilities
                logger.info("Drive upload is enabled but DriveUploadService is not yet implemented")
                logger.info(f"Files saved locally: HTML={html_file}, JSON={json_file}")

            except Exception as e:
                logger.error(f"Failed to upload summaries to Drive: {e}")
                # Don't fail the entire process if Drive upload fails
    
    def _call_ai_model(self, prompt: str) -> str:
        """Call available AI model with the given prompt."""
        last_error = None

        # Try Gemini API first (more reliable for authentication)
        if self.gemini_model:
            try:
                logger.info("Attempting to use Gemini API for content generation")
                response = self.gemini_model.generate_content(prompt)
                if response and response.text:
                    logger.info("Successfully generated content using Gemini API")
                    return response.text
                else:
                    raise ValueError("Empty response from Gemini API")
            except Exception as error:
                logger.warning(f"Gemini API call failed: {error}")
                last_error = error

        # Try Vertex AI as fallback
        if self.vertex_model:
            try:
                logger.info("Attempting to use Vertex AI for content generation")
                response = self.vertex_model.generate_content(prompt)
                if response and response.text:
                    logger.info("Successfully generated content using Vertex AI")
                    return response.text
                else:
                    raise ValueError("Empty response from Vertex AI")
            except Exception as error:
                logger.warning(f"Vertex AI call failed: {error}")
                last_error = error

        # If both failed, raise the last error
        if last_error:
            logger.error(f"All AI models failed. Last error: {last_error}")
            raise last_error
        else:
            raise ValueError("No AI models available")
    
    def _get_debrief_prompt(self, transcript_data: TranscriptData) -> str:
        """Get enhanced prompt for meeting debrief generation."""
        return f"""You are a skilled meeting analyst who specializes in extracting actionable insights from business conversations. Your role is to carefully listen to what was actually discussed and create a professional summary that captures the real decisions, commitments, and next steps.

## Your Core Principles

**Accuracy First**: Only include information that was genuinely discussed in the meeting. If someone didn't explicitly commit to something, don't assume they did. If a deadline wasn't mentioned, acknowledge that it's unspecified rather than guessing.

**Context Awareness**: Pay attention to the flow of conversation. Understand when someone is making a firm commitment versus just brainstorming ideas. Notice when questions are raised but left unresolved, and when follow-up discussions are needed.

**Natural Language**: Write summaries that sound professional and human, not robotic. Use clear, business-appropriate language that meeting participants would recognize as accurate.

## What to Extract

**Firm Decisions & Commitments**: Look for moments where the group reached consensus or where individuals made clear commitments. These might sound like:
- "Let's go with option A"
- "I'll handle the client presentation"
- "We've decided to postpone the launch"
- "Sarah will take the lead on this"

**Actionable Next Steps**: Capture specific tasks that people committed to, not general suggestions. Listen for:
- Direct assignments: "John, can you send me those numbers?"
- Personal commitments: "I'll follow up with the vendor"
- Group agreements: "We need to schedule a follow-up meeting"

**Unresolved Questions**: Notice when important topics were raised but not settled:
- Questions that sparked discussion but no clear answer
- Decisions that were deferred to later
- Issues that need more research or input

**Follow-up Needs**: Identify when the group recognized they need additional meetings or sessions:
- Technical deep-dives that were mentioned
- Strategy sessions that were proposed
- One-on-one conversations that were suggested

## Response Format

Provide your analysis as a clean JSON object with no additional text or formatting:

{{
    "meeting_metadata": {{
        "title": "string", // Extract or infer the meeting's purpose from context
        "attendees": ["string"], // Names of people who spoke or were mentioned as present
        "absent": ["string"], // People mentioned as absent, or empty array if none noted
        "date_processed_utc": "ISO 8601 string" // Current timestamp in UTC format
    }},
    "executive_summary": "string", // A concise 2-3 sentence summary of the meeting's purpose and key outcomes in natural business language
    "outcomes": [
        {{
            "decision": "string", // What was actually decided, in clear business language
            "owner": "string", // Who is primarily accountable for this outcome
            "rationale": "string", // The reasoning behind this decision as discussed
            "context": "string", // A relevant quote or paraphrase that supports this outcome
            "actions": [
                {{
                    "owner": "string", // The specific person who committed to this task
                    "task": "string", // What they committed to do, in actionable terms
                    "deadline": "string", // Exact deadline if mentioned, otherwise "Not specified"
                    "priority": "string" // HIGH/MEDIUM/LOW based on urgency expressed in discussion
                }}
            ]
        }}
    ],
    "open_questions": [
        {{
            "question": "string", // The unresolved question or issue, stated naturally
            "owner": "string", // Who was asked to investigate, or "Unassigned" if unclear
            "status": "string" // Current state: "Needs research", "Awaiting decision", "Deferred", etc.
        }}
    ],
    "working_sessions_needed": [
        {{
            "topic": "string", // The subject that needs a dedicated session
            "participants": "string", // Who should be involved in this session
            "goal": "string" // What this session should accomplish
        }}
    ]
}}

## Quality Guidelines

**Executive Summary**: Keep this brief and focused - just 2-3 sentences that capture the meeting's main purpose and key outcomes. Write as if giving a quick verbal update to someone who missed the meeting. Use natural business language and avoid unnecessary detail.

**Outcomes & Actions**: Only include decisions that were actually made and tasks that people genuinely committed to. If someone said "maybe I could look into that," that's not a firm commitment. If they said "I'll send you the report by Friday," that's a clear action item.

**Open Questions**: These should be substantive issues that the group recognized need resolution, not minor clarifications. Look for moments where someone said "we need to figure out..." or "that's a good question, let me research..."

**Working Sessions**: Only include follow-up meetings that were specifically discussed as needed, not general suggestions about "staying in touch."

**Language Quality**: Use professional, natural language throughout. Avoid corporate jargon unless it was actually used in the meeting. Write as a skilled business professional would communicate.

---

**TRANSCRIPT TO ANALYZE:**

{transcript_data.get_summary_text()}

---

**IMPORTANT**: Return only the JSON object above, with no additional text, explanations, or formatting. Ensure all strings use natural, professional language that accurately reflects what was discussed."""
    

    
    def _create_fallback_meeting_data(self, transcript_data: TranscriptData) -> MeetingData:
        """Create fallback meeting data when AI parsing fails."""
        # Try to extract basic information from transcript
        transcript_text = transcript_data.get_summary_text()
        participants = transcript_data.metadata.get('participants', [])

        # Create a basic summary from the transcript
        word_count = len(transcript_text.split())
        basic_summary = f"This meeting transcript contains {word_count} words of discussion"
        if participants:
            basic_summary += f" among {len(participants)} participants: {', '.join(participants[:3])}"
            if len(participants) > 3:
                basic_summary += f" and {len(participants) - 3} others"
        basic_summary += ". The AI summarization service encountered an issue processing this transcript, but the raw content is available for manual review."

        return MeetingData(
            meeting_metadata={
                "title": f"Meeting Debrief - {transcript_data.file_path.stem}",
                "attendees": participants,
                "absent": [],
                "date_processed_utc": datetime.now(timezone.utc).isoformat()
            },
            executive_summary=basic_summary,
            outcomes=[{
                "decision": "Manual review required",
                "owner": "Meeting organizer",
                "rationale": "AI processing encountered an issue with this transcript",
                "context": "Please review the original transcript for detailed meeting content",
                "actions": [{
                    "owner": "Meeting organizer",
                    "task": "Review transcript and extract key decisions manually",
                    "deadline": "Not specified",
                    "priority": "MEDIUM"
                }]
            }],
            open_questions=[{
                "question": "What were the key decisions and action items from this meeting?",
                "owner": "Meeting organizer",
                "status": "Requires manual transcript review"
            }],
            working_sessions_needed=[]
        )

    def _create_professional_html_template(self, meeting_data: MeetingData) -> str:
        """Create a professional HTML template that matches the design shown in the images."""
        # Extract data
        metadata = meeting_data.meeting_metadata
        title = metadata.get('title', 'Meeting Debrief')
        attendees = metadata.get('attendees', [])
        absent = metadata.get('absent', [])
        date_processed = metadata.get('date_processed_utc', datetime.now().isoformat())

        executive_summary = meeting_data.executive_summary
        outcomes = meeting_data.outcomes
        open_questions = meeting_data.open_questions
        working_sessions = meeting_data.working_sessions_needed

        # Generate tasks by owner
        all_actions = []
        for outcome in outcomes:
            all_actions.extend(outcome.get('actions', []))

        # Group tasks by owner
        tasks_by_owner = {}
        for action in all_actions:
            owner = action.get('owner', 'Unassigned')
            if owner not in tasks_by_owner:
                tasks_by_owner[owner] = []
            tasks_by_owner[owner].append(action)

        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - Meeting Debrief</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        h1 {{
            color: #1a1a1a;
            margin-bottom: 8px;
            font-size: 28px;
            font-weight: 600;
        }}
        .subtitle {{
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
        }}
        .metadata {{
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 32px;
            border: 1px solid #e9ecef;
        }}
        .metadata p {{
            margin: 4px 0;
            font-size: 14px;
        }}
        .metadata strong {{
            font-weight: 600;
        }}
        h2 {{
            color: #1a1a1a;
            font-size: 20px;
            font-weight: 600;
            margin: 32px 0 16px 0;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }}
        .summary {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 24px;
        }}
        .outcome {{
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }}
        .outcome-title {{
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }}
        .outcome-meta {{
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }}
        .context {{
            font-style: italic;
            color: #666;
            font-size: 14px;
            border-left: 3px solid #e9ecef;
            padding-left: 12px;
            margin-top: 12px;
        }}
        .owner-section {{
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }}
        .owner-header {{
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .owner-name {{
            font-weight: 600;
            color: #1a1a1a;
        }}
        .task-count {{
            font-size: 12px;
            color: #666;
        }}
        .tasks-table {{
            width: 100%;
            border-collapse: collapse;
        }}
        .tasks-table th {{
            background: #f8f9fa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }}
        .tasks-table td {{
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }}
        .priority {{
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }}
        .priority-high {{
            background: #fee;
            color: #c53030;
        }}
        .priority-medium {{
            background: #fffbeb;
            color: #d69e2e;
        }}
        .priority-low {{
            background: #f0f9ff;
            color: #2b6cb0;
        }}
        .question {{
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }}
        .session {{
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }}
        .no-items {{
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        <div class="subtitle">Debrief generated on {datetime.fromisoformat(date_processed.replace('Z', '+00:00')).strftime('%m/%d/%Y, %I:%M:%S %p')}</div>

        <div class="metadata">
            <p><strong>Present:</strong> {', '.join(attendees) if attendees else 'None specified'}</p>
            <p><strong>Absent:</strong> {', '.join(absent) if absent else 'None'}</p>
        </div>

        <h2>Executive Summary</h2>
        <div class="summary">
            {executive_summary or 'No executive summary provided.'}
        </div>

        <h2>Key Outcomes</h2>
        {self._generate_outcomes_html(outcomes)}

        <h2>Tasks</h2>
        {self._generate_tasks_html(tasks_by_owner)}

        <h2>Open Questions</h2>
        {self._generate_questions_html(open_questions)}

        <h2>Working Sessions Needed</h2>
        {self._generate_sessions_html(working_sessions)}
    </div>
</body>
</html>"""

    def _generate_outcomes_html(self, outcomes):
        """Generate HTML for outcomes section."""
        if not outcomes:
            return '<div class="no-items">No outcomes recorded.</div>'

        html = ""
        for outcome in outcomes:
            context_html = ""
            if outcome.get('context'):
                context_html = f'<div class="context"><strong>Context:</strong> {outcome["context"]}</div>'

            html += f"""
            <div class="outcome">
                <div class="outcome-title">{outcome.get('decision', 'No decision specified')}</div>
                <div class="outcome-meta">
                    <strong>Owner:</strong> {outcome.get('owner', 'Unassigned')} |
                    <strong>Rationale:</strong> {outcome.get('rationale', 'No rationale provided')}
                </div>
                {context_html}
            </div>
            """
        return html

    def _generate_tasks_html(self, tasks_by_owner):
        """Generate HTML for tasks section grouped by owner."""
        if not tasks_by_owner:
            return '<div class="no-items">No tasks assigned.</div>'

        html = ""
        for owner, tasks in tasks_by_owner.items():
            high_priority_count = sum(1 for task in tasks if task.get('priority') == 'HIGH')

            tasks_rows = ""
            for task in tasks:
                priority = task.get('priority', 'MEDIUM')
                priority_class = f"priority priority-{priority.lower()}"

                tasks_rows += f"""
                <tr>
                    <td>{task.get('task', 'No task description')}</td>
                    <td>{task.get('deadline', 'Not Specified')}</td>
                    <td><span class="{priority_class}">{priority}</span></td>
                </tr>
                """

            html += f"""
            <div class="owner-section">
                <div class="owner-header">
                    <div class="owner-name">{owner}</div>
                    <div class="task-count">{len(tasks)} tasks, {high_priority_count} high priority</div>
                </div>
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>Task</th>
                            <th>Deadline</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tasks_rows}
                    </tbody>
                </table>
            </div>
            """
        return html

    def _generate_questions_html(self, questions):
        """Generate HTML for open questions section."""
        if not questions:
            return '<div class="no-items">No open questions were left unresolved.</div>'

        html = ""
        for question in questions:
            html += f"""
            <div class="question">
                <div style="font-weight: 600; margin-bottom: 8px;">{question.get('question', 'No question specified')}</div>
                <div style="font-size: 14px; color: #666;">
                    <strong>Owner:</strong> {question.get('owner', 'Unassigned')} |
                    <strong>Status:</strong> {question.get('status', 'Pending')}
                </div>
            </div>
            """
        return html

    def _generate_sessions_html(self, sessions):
        """Generate HTML for working sessions section."""
        if not sessions:
            return '<div class="no-items">No working sessions needed.</div>'

        html = ""
        for session in sessions:
            html += f"""
            <div class="session">
                <div style="font-weight: 600; margin-bottom: 8px;">{session.get('topic', 'No topic specified')}</div>
                <div style="font-size: 14px; color: #666; margin-bottom: 4px;">
                    <strong>Participants:</strong> {session.get('participants', 'Not specified')}
                </div>
                <div style="font-size: 14px; color: #666;">
                    <strong>Goal:</strong> {session.get('goal', 'No goal specified')}
                </div>
            </div>
            """
        return html

def summarize_and_send_email(transcript_text: str, recipient_email: str, sender_email: str, smtp_password: str, smtp_host="smtp.gmail.com", smtp_port=587):
    """
    Accepts raw text, summarizes via AI, sends HTML summary to email.

    Args:
        transcript_text: Call with Nima Bousheri

You
OK, I think

Nima Bousheri
OK so here I'll give you a task here so check to see what because I know they they started building something. It's not completely from scratch. I think they built something but I'd like to get the intake form again just for data collection and intake form that I think you already have all the specs which says like client name address your number of life start of the campaign end of the campaign. What son is just basics very basic and see what we could do about getting that completed because we had a lot of feedback from not only USI but like Seabi like 10 people from Seabi showed up to that

You
Yeah,

Nima Bousheri
That happy hour that we had

You
that real quick yeah tell me about the happy hour man how'd it go?

Nima Bousheri
So it was good they had like 10 brokers from sea biz they had some gallery like 34 people from Gallagher show up so it was it was good to set up was awesome lately I had a CK like set up the table. Send pictures of it like set up the table with all of the finger foods and burgers and all that we did at STK in San Diego.

You
Nice nice

Nima Bousheri
The super nice set up, but they also had like swag like little bags and all that kind of stuff to give away so the happy hour was awesome. The event there was one thing that the freaking company didn't send us the podium and so they had good thing I had a table with a tablecloth as much as I don't really like our

You
OK

Nima Bousheri
tablecloth I I'd like the one that's a little bit more tight on tables, but it worked fine display look good. She's like it looks like a carry-on bag. It's kind of full completely after that which is awesome so basically. We gave away like 500 something Zuma health sunglasses so there's 500 some people walking around with with you know like the wooden ones that look like right with the wood on the side yeah I'll send you a picture of

You
Oh sweet Nice

Nima Bousheri
like super cool set up but they had that on the table they had a lot of really good leads that came out of it like Methodist hospital came A couple of that that's why I didn't want to kill to start not reaching because there's some really hot leads that they talk to that. I don't want to outreach too yet like in that format it's gonna be more Nikki Bailey, but yeah, so it was they did a really good job with that but the feedback we got was two things. Most people said our pricing is good but there's a few special with the new increase right there's a few clients like the Hilty one that we lost because the Pricing. And USI like if you move to two you'll be the most expensive one of the group and some people will just be turned off by so what we decided to do is we're gonna have a package that's that it's a dollar but it's automated with no engagement manager like it's just you roll in you get nothing you get basically that platform you run it because that's what Willow is doing. They're offering like basically no account management and just a platform and if you want.

You
I thought you said the people are complaining about that

Nima Bousheri
They are so for the pricing though we're gonna just look like we have that available for RFPs as like because you don't when you fill out an RFP they don't they don't listen to you. They just look at the RFP on paper and then they go OK we'll talk to you. You know 12 and three and they based on price so once we get in, we can look like we're starting at one and then we can say well if you want the customized newsletters and marketing materials and all that other stuff, then a gator manager, it's another dollar. Right so you have a tear of the one in the regular 23 and four dollar pricing so the US that we had a call yesterday or this morning and they liked the idea that most people are gonna just go for the higher prices because they're gonna want an engagement manager, but at least it looks on paper like you have it and if we can roll it out basically automatically it's like a self-serve solution So we have that kind of check the box do you wanna do your own thing and you wanna do it for cheap here? It is at a dollar but in order to do it we have to have that automated on board.

You
It has to be a minute you have to put a minimum monthly and you should do this as a test

Nima Bousheri
We do have that so the minimum the minimum monthly that we're gonna put on it is gonna be like $500 minimum a month which basically equates about and I was like 100 and like I think we ended up putting like 200 lives which kind of put it for 150 lie I forgot what we decided on when we were talking so there's a minimum so they can't just have some random lie But

You
Nikki told me we had some contracts that were like $150 a month or something or two and I'm like what the hell why

Nima Bousheri
Yeah, cause we've never had the minimum but we're putting now I think that's the other chance we're gonna put the minimum amount which is gonna be for the year they said a little over 5000 some change but basically

You
Yeah

Nima Bousheri
$500 minimum that's what the changes we're gonna put on there

You
Yeah, let's add that that other package I told you about that was the more premium one that has the disease visit because I talked to him. He said they're like pretty much they were ready they just they haven't looked at it for so they have to go check the code.

Nima Bousheri
Yeah, let him take a look at that. I think with that we gotta add that like because the really the buzz right now

You
And you

Nima Bousheri
around wellness is around financial well-being which there's a whole Nother conversation I wanna get into right now and emotional so we had discussions on what we think we should add in there but if we're gonna do DM, I think we put the premium package. You also have an emotional well-being module that's kind of lump in with it or maybe as a separate add-on because a lot of companies want that right now.

You
Yeah Yeah, OK I mean I can. I can. I can jump on it

Nima Bousheri
I mean, that's like the hot topic Just see two things first first see where they're at

You
That's fine

Nima Bousheri
with that development of that

You
Yeah, I'm on boarding

Nima Bousheri
intake form to create the platform because what we really and then we're having some changes like infrastructure and I think Perry would like this and it's kind of an engagement manager because one thing I wanna remove from engagement managers is All the task they have to do when it comes to like design coordinating with the design team like setting up the platform like going in and setting up a challenge making sure they're doing it right because as we were scaling and there's so many functionality of the platform, the engagement managers learning curve is hard for them to like know I don't even know if you tell me how to set up a freaking child go set up this child. I have no idea to do it, right Properly for like all these different challenges that we have now so we're basically putting in Kia, which is Cassie's daughter that we used to work for us left finish school. I'm now back as like a coordinator and he's gonna be in kind of in the middle and then who knows the system in and out who Can set up the challenges are very well to work with forever. She'll be the one setting up like the challenges of an engagement mandarin needs these four challenges set up. Just need to give them the details start date and date. Here's a challenge. Here's what I want to make focus more customer, relation management and meetings and strategy and stuff and not to worry about having to properly set up a challenge in the system and like that stuff so What do you think about that if you think that'll help?

You
time Like looking it up and making sure I did it right rather than

You
challenge that I could copy and paste that matches the one that my client wants.

Nima Bousheri
Exactly yeah so I'm taking that out of you guys responsibilities it's gonna take a little bit of time to do it because there's just so many clients that we have like because you only have so much bandwidth but I'm gonna take that out of you guys responsibility and put it onto someone else so that you guys can open up more time for you. Hell you got manage more clients but also like don't get frustrated with trying to set up a challenge or maybe it set up wrong and then the client gets upset and all that kind of stuff so And then that also the coordinator will help kind of between the Carlos and design team with like let's create the poster. Let's create the billboard like all that kind of stuff will be happening in the background and the reason I'm bringing it up I think some of that stuff going back to creating the process is for some stuff I think you can have it where I just set up some of the other pieces can be created at least initialize from the front end to the back end where like the design team then get all these prompts to create all that stuff so longer conversation, but I think that's kind of some of the stuff that we talked about Kind of creating I guess pivoting too

You
OK, anything else?

Nima Bousheri
What else is they gonna tell you Amazon do you have access to the do we have a Amazon account because I saw like a bunch of

You
No

Nima Bousheri
charges on the on the southwest card this past month And I'm

You
I don't even have access to the Southwest card. I don't even oh the car that you gave me they shouldn't have been random.

Nima Bousheri
Yeah

You
Amazon charges there was.

Nima Bousheri
There's a bunch of like market

You
No Nita,

Nima Bousheri
marketplace and all that I just need to make sure they look no that's different. Yeah those are those are there. I'm not looking at those like we have LinkedIn

You
LinkedIn, LinkedIn, and Google those two

Nima Bousheri
chargers we have a WS on that card so all that those already know, but like this past month, there was like a ton of charges randomly that were like.

You
Just run around the ones that you you think or even mine even if you think I know them, but I'm saying the ones that you don't know and the ones you think that that you think are mine, just run by me and I'll tell you but I need to talk about budget too with marketing so why

Nima Bousheri
Well, I don't even know if they're that's what I'm saying cause I just ran about a emperor

You
don't you finish first and then I'll get into it

Nima Bousheri
But is there a W like is there a mark or like Amazon marketplace charges that would take place on our card for like stuff? That's a

You
Wait

Nima Bousheri
escalated right now right cause that's only AWS

You
We don't have any health AWS marketplace not that I know of

Nima Bousheri
That's what I thought yeah cause they're just act like I said the random. The only person left that we're gonna ask. Is that Nicole who's kind of random stuff and sometimes she buys like gift cards and things like that so I asked Kezia and it wasn't her. So I didn't know if it was like to something else that you had so I just wanna make sure

You
No, I'm yeah whenever you're done I'll tell you about the marketing budget. I'm actually making some adjustments right now.

Nima Bousheri
Yeah, take a look at it but just see whenever you get home like if you have access to Amazon see if that card is in tight to anything else besides AWS stuff

You
I don't have an Amazon anything or there's only AWS which I haven't touched for years

Nima Bousheri
That's it man just we need to follow up with the with Nikki and them for all those people from and then there's another one coming up in September we're gonna talk about should we attend that one or not? Your birthday is in Vegas, so you might wanna go chaperone the team

You
Yeah, yeah, OK. I just like run run by us and then we can talk about it cause we definitely didn't do everything we could do in this one and my trip to that Idaho was probably like the worst time ever because I got.

Nima Bousheri
No a lot man did a really good job. I wouldn't. I would not frame it like that at all.

You
I know you guys took care of it but when I came back, I didn't even realize the event was like that day that our team at the event tomorrow looks like holy shit is tomorrow like that I just got thrown off by that trip was just right in the middle, but that's that's fine

Nima Bousheri
Yeah, no it's like they did a really good job and they connected with ton of people they got business cards they they they did a really good job

You
OK, so the number one is like I know Nikhil's working on this so let me know when you're ready to transition cause I have a dump of things I've been holding for weeks I won't give you everything but there's a few things

Nima Bousheri
You're good that's all that just those two things

Nima Bousheri
I will give it a little bit more but here's what I'm thinking based on what my feedback if we take some of these resources and put it towards targeting like there's two or three rockers that we really need to target one is locked in like we have very very little engagement, and we have to engage with them because their large brokers from I have extremely low engagement with us. I don't even think about one client with them number two you'll appreciate this company came up to our booth. That is super interested in working with us brokers firm called.

Thank You,

Sachin Shah
Elevation AI
M- 281-235-7018

        recipient_email: "<EMAIL>",
        sender_email: "<EMAIL>",
        smtp_password: "<EMAIL>!@#"

    Returns:
        HTML file path if sent successfully
    """
    try:
        transcript_data = TranscriptData(
            file_path=Path("manual_input.txt"),
            content=transcript_text,
            metadata={
                "word_count": len(transcript_text.split()),
                "meeting_time": datetime.now().isoformat()
            }
        )

        summarizer = AISummarizer()
        summary = summarizer.summarize_transcript(transcript_data)

        msg = EmailMessage()
        msg['Subject'] = summary.get_email_subject()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg.set_content("Hi,\n\nPlease find the attached meeting summary.\n\n- Your AI Assistant")

        html_path = summary.meeting_data.html_file_path
        if html_path and Path(html_path).exists():
            with open(html_path, 'rb') as f:
                msg.add_attachment(f.read(), maintype='text', subtype='html', filename=Path(html_path).name)
        else:
            msg.set_content(summary.html_summary)

        with smtplib.SMTP(smtp_host, smtp_port) as smtp:
            smtp.starttls()
            smtp.login(sender_email, smtp_password)
            smtp.send_message(msg)

        logger.info(f"Summary emailed to {recipient_email}")
        return html_path

    except Exception as e:
        logger.error(f"Failed to summarize and send email: {e}")
        return None