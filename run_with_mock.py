#!/usr/bin/env python3
"""
Run the AI summarizer with mock AI responses (no API keys needed).
This demonstrates the full functionality including email preparation.
"""

import os
from pathlib import Path
from datetime import datetime
from ai_summarizer import TranscriptData, MeetingData, MeetingSummary

# Import our mock summarizer
from test_summarizer import MockAISummarizer

def run_mock_email_summary():
    """Run the email summary with mock AI (safe for testing)."""
    
    print("🚀 Running AI Meeting Summarizer (Mock Mode)")
    print("=" * 50)
    
    # The transcript from your run_summary_email.py file
    transcript_text = """Call with <PERSON><PERSON>
OK, I think

<PERSON><PERSON> so here I'll give you a task here so check to see what because I know they they started building something. It's not completely from scratch. I think they built something but I'd like to get the intake form again just for data collection and intake form that I think you already have all the specs which says like client name address your number of life start of the campaign end of the campaign. What son is just basics very basic and see what we could do about getting that completed because we had a lot of feedback from not only USI but like <PERSON><PERSON> like 10 people from <PERSON>bi showed up to that

You
Yeah,

<PERSON><PERSON>
That happy hour that we had

You
that real quick yeah tell me about the happy hour man how'd it go?

<PERSON><PERSON>
So it was good they had like 10 brokers from sea biz they had some gallery like 34 people from <PERSON> show up so it was it was good to set up was awesome lately I had a CK like set up the table. Send pictures of it like set up the table with all of the finger foods and burgers and all that we did at STK in San Diego.

You
Nice nice

Nima Bousheri
The super nice set up, but they also had like swag like little bags and all that kind of stuff to give away so the happy hour was awesome. The event there was one thing that the freaking company didn't send us the podium and so they had good thing I had a table with a tablecloth as much as I don't really like our

You
OK

Nima Bousheri
tablecloth I I'd like the one that's a little bit more tight on tables, but it worked fine display look good. She's like it looks like a carry-on bag. It's kind of full completely after that which is awesome so basically. We gave away like 500 something Zuma health sunglasses so there's 500 some people walking around with with you know like the wooden ones that look like right with the wood on the side yeah I'll send you a picture of

You
Oh sweet Nice

Nima Bousheri
like super cool set up but they had that on the table they had a lot of really good leads that came out of it like Methodist hospital came A couple of that that's why I didn't want to kill to start not reaching because there's some really hot leads that they talk to that. I don't want to outreach too yet like in that format it's gonna be more Nikki Bailey, but yeah, so it was they did a really good job with that but the feedback we got was two things. Most people said our pricing is good but there's a few special with the new increase right there's a few clients like the Hilty one that we lost because the Pricing. And USI like if you move to two you'll be the most expensive one of the group and some people will just be turned off by so what we decided to do is we're gonna have a package that's that it's a dollar but it's automated with no engagement manager like it's just you roll in you get nothing you get basically that platform you run it because that's what Willow is doing. They're offering like basically no account management and just a platform and if you want."""
    
    try:
        # Create transcript data
        transcript_data = TranscriptData(
            file_path=Path("business_call.txt"),
            content=transcript_text,
            metadata={
                "word_count": len(transcript_text.split()),
                "meeting_time": datetime.now().isoformat(),
                "participants": ["You", "Nima Bousheri"]
            }
        )
        
        # Use mock summarizer (no API keys needed)
        print("📝 Initializing AI Summarizer (Mock Mode)...")
        summarizer = MockAISummarizer()
        
        # Generate summary
        print("🤖 Generating meeting summary...")
        summary = summarizer.summarize_transcript(transcript_data)
        
        print("✅ Summary generated successfully!")
        print()
        
        # Display results
        print("📧 EMAIL DETAILS:")
        print(f"   Subject: {summary.get_email_subject()}")
        print(f"   Participants: {summary.get_participants_summary()}")
        print()
        
        print("📄 FILES CREATED:")
        print(f"   HTML: {summary.meeting_data.html_file_path}")
        print(f"   JSON: {summary.meeting_data.json_file_path}")
        print()
        
        print("📋 EXECUTIVE SUMMARY:")
        print(f"   {summary.meeting_data.executive_summary}")
        print()
        
        print("🎯 KEY OUTCOMES:")
        for i, outcome in enumerate(summary.meeting_data.outcomes, 1):
            print(f"   {i}. {outcome['decision']}")
            print(f"      Owner: {outcome['owner']}")
            print(f"      Rationale: {outcome['rationale']}")
            print()
        
        print("📋 ACTION ITEMS:")
        action_count = 0
        for outcome in summary.meeting_data.outcomes:
            for action in outcome.get('actions', []):
                action_count += 1
                print(f"   {action_count}. {action['task']}")
                print(f"      Owner: {action['owner']}")
                print(f"      Priority: {action['priority']}")
                print(f"      Deadline: {action['deadline']}")
                print()
        
        print("❓ OPEN QUESTIONS:")
        for i, question in enumerate(summary.meeting_data.open_questions, 1):
            print(f"   {i}. {question['question']}")
            print(f"      Owner: {question['owner']}")
            print(f"      Status: {question['status']}")
            print()
        
        print("🔧 WORKING SESSIONS NEEDED:")
        for i, session in enumerate(summary.meeting_data.working_sessions_needed, 1):
            print(f"   {i}. {session['topic']}")
            print(f"      Participants: {session['participants']}")
            print(f"      Goal: {session['goal']}")
            print()
        
        print("=" * 50)
        print("✨ SUCCESS! The AI summarizer is working correctly.")
        print("📁 Check the 'meeting_summaries' folder for the generated files.")
        print()
        print("💡 To use with real AI:")
        print("   1. Set GEMINI_API_KEY environment variable")
        print("   2. Or configure Google Cloud credentials for Vertex AI")
        print("   3. Run the original run_summary_email.py script")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_mock_email_summary()
    if not success:
        print("\n💥 Something went wrong. Check the error messages above.")
